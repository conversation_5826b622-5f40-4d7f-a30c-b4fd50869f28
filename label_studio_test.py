#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Label Studio 回归测试脚本
用于自动化测试OCR结果与标注数据的一致性
"""

import json
import requests
import base64
from io import BytesIO
from PIL import Image
import sys
import os
from typing import Dict, List, Any, Tuple
import difflib
from openai import OpenAI


class LabelStudioTester:
    def __init__(self, json_file_path: str, ocr_endpoint: str = "http://localhost:9000/ocr/prediction"):
        """
        初始化测试器
        
        Args:
            json_file_path: Label Studio导出的JSON文件路径
            ocr_endpoint: OCR服务的API端点
        """
        self.json_file_path = json_file_path
        self.ocr_endpoint = ocr_endpoint
        self.tasks_data = None
        self.continue_on_wrong = True  # 控制是否在错误时继续

        self.llm_client = OpenAI(base_url="http://************:8080/v1", api_key="")
        self.llm_model = "MiniCPM-V-4_5-F16.gguf"

        # 字符对等价名单 - 这些字符对被认为是相等的
        self.char_equals_pairs = {
            # 标点符号
            (',', '，'),  # 英文逗号 vs 中文逗号
            ('.', '。'),  # 英文句号 vs 中文句号
            ('?', '？'),  # 英文问号 vs 中文问号
            ('!', '！'),  # 英文感叹号 vs 中文感叹号
            (':', '：'),  # 英文冒号 vs 中文冒号
            (';', '；'),  # 英文分号 vs 中文分号
            ('(', '（'),  # 英文左括号 vs 中文左括号
            (')', '）'),  # 英文右括号 vs 中文右括号
            ('[', '［'),  # 英文左方括号 vs 中文左方括号
            (']', '］'),  # 英文右方括号 vs 中文右方括号
            ('{', '｛'),  # 英文左花括号 vs 中文左花括号
            ('}', '｝'),  # 英文右花括号 vs 中文右花括号
            ('\u0022', '\u201C'),  # 英文双引号 vs 中文左双引号 (U+0022 vs U+201C)
            ('\u0022', '\u201D'),  # 英文双引号 vs 中文右双引号 (U+0022 vs U+201D)
            ('-', '－'),  # 英文连字符 vs 中文连字符
            ('_', '＿'),  # 英文下划线 vs 中文下划线
            # 数字
            ('0', '０'), ('1', '１'), ('2', '２'), ('3', '３'), ('4', '４'),
            ('5', '５'), ('6', '６'), ('7', '７'), ('8', '８'), ('9', '９'),
        }

        # 创建双向映射字典，支持一对多映射，方便查找
        self.char_equals_map = {}
        for char1, char2 in self.char_equals_pairs:
            # 为每个字符创建等价字符集合
            if char1 not in self.char_equals_map:
                self.char_equals_map[char1] = set()
            if char2 not in self.char_equals_map:
                self.char_equals_map[char2] = set()

            # 添加双向映射
            self.char_equals_map[char1].add(char2)
            self.char_equals_map[char2].add(char1)

    def load_annotation_data(self) -> bool:
        """
        读取Label Studio导出的标注数据JSON文件
        
        Returns:
            bool: 读取是否成功
        """
        try:
            print(f"正在读取标注数据文件: {self.json_file_path}")
            with open(self.json_file_path, "r", encoding="utf-8") as f:
                self.tasks_data = json.load(f)
            print(f"成功读取 {len(self.tasks_data)} 个标注任务")
            return True
        except FileNotFoundError:
            print(f"错误: 找不到文件 {self.json_file_path}")
            return False
        except json.JSONDecodeError as e:
            print(f"错误: JSON文件格式不正确 - {e}")
            return False
        except Exception as e:
            print(f"错误: 读取文件时发生异常 - {e}")
            return False

    def download_image_as_base64(self, image_url: str) -> str:
        """
        从URL下载图片并转换为base64格式
        
        Args:
            image_url: 图片的URL
            
        Returns:
            str: base64编码的图片数据，失败时返回空字符串
        """
        try:
            response = requests.get(image_url, timeout=30)
            response.raise_for_status()

            # 将图片数据转换为base64
            image_data = response.content
            base64_data = base64.b64encode(image_data).decode('utf-8')
            return base64_data

        except requests.exceptions.RequestException as e:
            print(f"图片下载失败: {e}")
            return ""
        except Exception as e:
            print(f"图片处理异常: {e}")
            return ""

    def get_ocr_result_from_llm(self, base64_image: str) -> str:
        with open('few_shot_img_base64.txt', 'r', encoding='utf-8') as f:
            img1_base64 = f.read().strip()

        messages = [
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": img1_base64
                        }
                    },
                    {
                        "type": "text",
                        "text": "图片中的文本"
                    }
                ]
            },
            {
                "role": "assistant",
                "content": [
                    {
                        "type": "text",
                        "text": "1、膀胱测压测定：从留置尿管低速充盈灌注，膀胱顺应性正常，稳定性下降，储尿期充盈157ml开始逼尿肌出现多次无抑制性收缩无漏尿，初感尿意127ml，强烈尿感193ml，膀胱感觉敏感、容量减少；2、排尿期压力-流率测定：腹压协助排尿，膀胱收缩力弱，可疑下尿路梗阻。"
                    }
                ]
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": f"data:image/png;base64,{base64_image}"
                        }
                    },
                    {
                        "type": "text",
                        "text": "图片中的文本"
                    }
                ]
            }
        ]

        completion = self.llm_client.chat.completions.create(
            model=self.llm_model,
            messages=messages,
            top_p=0.8,
            temperature=0.7,
        )
        return completion.choices[0].message.content

    def get_ocr_result(self, base64_image: str) -> str:
        """
        调用OCR服务获取文本识别结果
        
        Args:
            base64_image: base64编码的图片数据
            
        Returns:
            str: OCR识别的文本结果，失败时返回空字符串
        """
        try:
            request_data = {
                "dataType": "image",
                "matchArea": {
                    "page": 0,
                    "startX": 0,
                    "startY": 0,
                    "cutHeight": -1,
                    "cutWidth": -1,
                    "recModelId": "ppocrv4_doc_server"
                },
                "images": [base64_image]
            }

            response = requests.post(
                self.ocr_endpoint,
                json=request_data,
                timeout=60,
                headers={'Content-Type': 'application/json'}
            )
            response.raise_for_status()

            result = response.json()
            if "ocrResults" in result and len(result["ocrResults"]) > 0:
                ocr_text = result["ocrResults"][0].get("text_result", "")
                return ocr_text
            else:
                print("OCR服务返回空结果")
                return ""

        except requests.exceptions.RequestException as e:
            print(f"OCR请求失败: {e}")
            return ""
        except json.JSONDecodeError as e:
            print(f"OCR返回JSON格式错误: {e}")
            return ""
        except Exception as e:
            print(f"OCR服务异常: {e}")
            return ""

    def show_string_differences(self, ocr_text: str, target_text: str) -> None:
        """
        显示两个字符串的详细差异
        
        Args:
            ocr_text: OCR识别的文本
            target_text: 目标文本
        """
        print("\n" + "=" * 60)
        print("字符串差异分析")
        print("=" * 60)

        # 显示原始文本（截断显示）
        max_display_length = 150
        print(f"OCR文本 (长度:{len(ocr_text)}):")
        print(f"   '{ocr_text[:max_display_length]}{'...' if len(ocr_text) > max_display_length else ''}'")
        print(f"目标文本 (长度:{len(target_text)}):")
        print(f"   '{target_text[:max_display_length]}{'...' if len(target_text) > max_display_length else ''}'")
        print()

        # 字符级差异分析
        self._show_character_differences(ocr_text, target_text)

        print("=" * 60)

    def _show_character_differences(self, ocr_text: str, target_text: str) -> None:
        """
        显示字符级别的差异
        
        Args:
            ocr_text: OCR识别的文本
            target_text: 目标文本
        """
        print("字符级差异:")

        # 找出第一个不同的字符位置
        min_length = min(len(ocr_text), len(target_text))
        first_diff_pos = -1

        for i in range(min_length):
            if not self._chars_are_equal(ocr_text[i], target_text[i]):
                first_diff_pos = i
                break

        if first_diff_pos >= 0:
            print(f"   首个差异位置: {first_diff_pos}")

            # 显示差异位置周围的文本
            context_range = 15
            start_pos = max(0, first_diff_pos - context_range)
            end_pos = min(len(ocr_text), first_diff_pos + context_range + 1)

            ocr_context = ocr_text[start_pos:end_pos]
            print(f"   OCR: '{ocr_context}'")

            # 标记差异位置
            marker_pos = first_diff_pos - start_pos
            # 计算实际显示位置，考虑中文字符的显示宽度
            display_width = 9  # "   OCR: '" 的显示宽度
            for i, char in enumerate(ocr_context):
                if i >= marker_pos:
                    break
                # 判断是否为宽字符（中文、日文、韩文等）
                if self._is_wide_char(char):
                    display_width += 2
                else:
                    display_width += 1

            marker = ' ' * display_width + '^'
            print(f"{marker}")

            end_pos_target = min(len(target_text), first_diff_pos + context_range + 1)
            target_context = target_text[start_pos:end_pos_target]
            print(f"   目标: '{target_context}'")

            # 显示具体的字符差异
            if first_diff_pos < len(ocr_text) and first_diff_pos < len(target_text):
                ocr_char = ocr_text[first_diff_pos]
                target_char = target_text[first_diff_pos]
                print(f"   差异: OCR='{ocr_char}' vs 目标='{target_char}'")

        # 统计差异信息
        diff_count = 0
        for i in range(min_length):
            if not self._chars_are_equal(ocr_text[i], target_text[i]):
                diff_count += 1
        diff_count += abs(len(ocr_text) - len(target_text))  # 加上长度差异

        if len(ocr_text) != len(target_text):
            print(f"   长度差异: {abs(len(ocr_text) - len(target_text))} 个字符")
        print(f"   总计差异: {diff_count} 个字符")

    def _is_wide_char(self, char: str) -> bool:
        """
        判断字符是否为宽字符（中文、日文、韩文等）
        
        Args:
            char: 要判断的字符
            
        Returns:
            bool: True表示是宽字符
        """
        # 获取字符的Unicode分类
        import unicodedata

        # 东亚宽字符的Unicode范围
        code = ord(char)

        # 中文字符范围
        if 0x4e00 <= code <= 0x9fff:  # CJK统一汉字
            return True
        if 0x3400 <= code <= 0x4dbf:  # CJK扩展A
            return True
        if 0x20000 <= code <= 0x2a6df:  # CJK扩展B
            return True
        if 0x2a700 <= code <= 0x2b73f:  # CJK扩展C
            return True
        if 0x2b740 <= code <= 0x2b81f:  # CJK扩展D
            return True

        # 日文字符
        if 0x3040 <= code <= 0x309f:  # 平假名
            return True
        if 0x30a0 <= code <= 0x30ff:  # 片假名
            return True

        # 韩文字符
        if 0xac00 <= code <= 0xd7af:  # 韩文音节
            return True
        if 0x1100 <= code <= 0x11ff:  # 韩文字母
            return True

        # 全角字符
        if 0xff00 <= code <= 0xffef:  # 全角ASCII、全角标点
            return True

        # 其他常见的宽字符
        if 0x2e80 <= code <= 0x2eff:  # CJK部首补充
            return True
        if 0x2f00 <= code <= 0x2fdf:  # 康熙部首
            return True
        if 0x3000 <= code <= 0x303f:  # CJK符号和标点
            return True

        return False

    def _chars_are_equal(self, char1: str, char2: str) -> bool:
        """
        检查两个字符是否相等（包括等价字符对）
        
        Args:
            char1: 第一个字符
            char2: 第二个字符
            
        Returns:
            bool: True表示字符相等或等价
        """
        if char1 == char2:
            return True

        # 检查是否在等价字符对中
        equal_chars = self.char_equals_map.get(char1, set())
        return char2 in equal_chars

    def _strings_are_equal(self, str1: str, str2: str) -> bool:
        """
        使用等价字符对规则比较两个字符串是否相等
        
        Args:
            str1: 第一个字符串
            str2: 第二个字符串
            
        Returns:
            bool: True表示字符串相等或等价
        """
        str1 = str1.strip()
        str2 = str2.strip()

        if len(str1) != len(str2):
            return False

        for i in range(len(str1)):
            if not self._chars_are_equal(str1[i], str2[i]):
                return False

        return True

    def ask_user_continue(self, task_index: int, total_tasks: int, ocr_text: str = None,
                          target_text: str = None) -> bool:
        """
        询问用户是否继续测试
        
        Args:
            task_index: 当前任务索引
            total_tasks: 总任务数
            ocr_text: OCR识别的文本（用于重新显示差异）
            target_text: 目标文本（用于重新显示差异）
            
        Returns:
            bool: True表示继续，False表示退出
        """
        print(f"\n当前进度: {task_index}/{total_tasks}")
        print("发现OCR结果与目标文本不匹配！")

        while True:
            try:
                choice = input(
                    "\n请选择操作:\n1. 继续下一个任务 (c/continue)\n2. 退出测试 (q/quit)\n3. 重新查看详细差异 (d/diff)\n请输入选择: ").strip().lower()

                if choice in ['c', 'continue', '1', '继续']:
                    return True
                elif choice in ['q', 'quit', '2', '退出']:
                    print("用户选择退出测试")
                    return False
                elif choice in ['d', 'diff', '3', '差异']:
                    if ocr_text is not None and target_text is not None:
                        self.show_string_differences(ocr_text, target_text)
                    else:
                        print("无法显示差异：缺少文本数据")
                    continue
                else:
                    print("无效选择，请重新输入")

            except KeyboardInterrupt:
                print("\n\n用户中断测试")
                return False
            except EOFError:
                print("\n\n输入结束，退出测试")
                return False

    def update_annotation_result(self, task: Dict, ocr_text: str, target_text: str) -> bool:
        """
        更新标注结果中的OCR文本和正确性判断
        
        Args:
            task: 标注任务数据
            ocr_text: OCR识别的文本
            target_text: 目标文本
            
        Returns:
            bool: 更新是否成功
        """
        try:
            if not task.get("annotations"):
                print("警告: 任务中没有标注数据")
                return False

            # 判断OCR结果是否正确（使用等价字符对规则）
            is_correct = self._strings_are_equal(ocr_text, target_text)
            correct_value = "Correct" if is_correct else "Wrong"

            updated = False
            for annotation in task["annotations"]:
                if not annotation.get("result"):
                    continue

                for result_item in annotation["result"]:
                    # 更新OCR文本
                    if result_item.get("from_name") == "ocr_text":
                        result_item["value"]["text"] = [ocr_text]
                        updated = True

                    # 更新正确性判断
                    elif result_item.get("from_name") == "isCorrect":
                        result_item["value"]["choices"] = [correct_value]
                        updated = True

            return updated

        except Exception as e:
            print(f"错误: 更新标注结果时发生异常 - {e}")
            return False

    def save_updated_data(self, output_path: str = None) -> bool:
        """
        将更新后的标注数据保存到JSON文件
        
        Args:
            output_path: 输出文件路径，如果为None则覆盖原文件
            
        Returns:
            bool: 保存是否成功
        """
        try:
            save_path = output_path or self.json_file_path
            print(f"正在保存更新后的标注数据到: {save_path}")

            with open(save_path, "w", encoding="utf-8") as f:
                json.dump(self.tasks_data, f, ensure_ascii=False, indent=2)

            print("标注数据保存成功")
            return True

        except Exception as e:
            print(f"错误: 保存文件时发生异常 - {e}")
            return False

    def run_regression_test(self) -> Dict[str, int]:
        """
        运行完整的回归测试流程
        
        Returns:
            Dict[str, int]: 测试结果统计
        """
        print("开始Label Studio回归测试")

        # 初始化统计数据
        stats = {
            "total": 0,
            "processed": 0,
            "correct": 0,
            "wrong": 0,
            "errors": 0
        }

        # 1. 读取标注数据
        if not self.load_annotation_data():
            return stats

        # 2. 遍历标注数据
        for i, task in enumerate(self.tasks_data, 1):
            print(f"\n处理任务 {i}/{len(self.tasks_data)}")
            stats["total"] += 1

            try:
                # 获取任务ID
                task_id = task.get("id", f"task_{i}")

                # 获取图片URL
                image_url = task.get("data", {}).get("url", "")
                if not image_url:
                    print("警告: 任务中没有图片URL")
                    stats["errors"] += 1
                    continue

                # 获取目标文本
                target_text = ""
                if task.get("annotations"):
                    for annotation in task["annotations"]:
                        for result_item in annotation.get("result", []):
                            if result_item.get("from_name") == "target_text":
                                target_text = result_item.get("value", {}).get("text", [""])[0]
                                break

                if not target_text:
                    print("任务中没有目标文本")
                    stats["errors"] += 1
                    continue

                # 3. 下载图片并转换为base64
                base64_image = self.download_image_as_base64(image_url)
                if not base64_image:
                    stats["errors"] += 1
                    continue

                # 4. 获取OCR结果
                ocr_text = self.get_ocr_result_from_llm(base64_image)
                if not ocr_text:
                    stats["errors"] += 1
                    continue

                # 5. 更新标注结果
                if self.update_annotation_result(task, ocr_text, target_text):
                    stats["processed"] += 1

                    # 统计正确性（使用等价字符对规则）
                    is_correct = self._strings_are_equal(ocr_text, target_text)
                    if is_correct:
                        stats["correct"] += 1
                        print("OCR结果正确")
                    else:
                        stats["wrong"] += 1
                        print("OCR结果不正确")
                        print(f"任务ID: {task_id}")
                        print(f"图片URL: {image_url}")

                        # 显示字符串差异
                        self.show_string_differences(ocr_text, target_text)

                        # 询问用户是否继续
                        if not self.ask_user_continue(i, len(self.tasks_data), ocr_text, target_text):
                            print("测试被用户中断")
                            break
                else:
                    stats["errors"] += 1

            except Exception as e:
                print(f"错误: 处理任务时发生异常 - {e}")
                stats["errors"] += 1

        # 6. 保存更新后的数据
        if stats["processed"] > 0:
            self.save_updated_data()

        # 输出测试结果
        self.print_test_results(stats)

        return stats

    def print_test_results(self, stats: Dict[str, int]):
        """
        打印测试结果统计
        
        Args:
            stats: 测试结果统计数据
        """
        print("\n测试结果统计")
        print("=" * 40)
        print(f"总任务数: {stats['total']}")
        print(f"成功处理: {stats['processed']}")
        print(f"处理错误: {stats['errors']}")

        if stats['processed'] > 0:
            print(f"OCR正确: {stats['correct']} ({stats['correct'] / stats['processed']:.1%})")
            print(f"OCR错误: {stats['wrong']} ({stats['wrong'] / stats['processed']:.1%})")

        print("=" * 40)


def main():
    """主函数"""
    # 使用默认的测试文件路径
    json_file_path = "/Users/<USER>/Downloads/project-4-at-2025-09-01-07-38-d49fca5d.json"

    # 检查文件是否存在
    if not os.path.exists(json_file_path):
        print(f"错误: 测试文件不存在 - {json_file_path}")
        print("请确保文件路径正确，或者修改脚本中的文件路径")
        sys.exit(1)

    # 创建测试器并运行测试
    tester = LabelStudioTester(json_file_path)
    results = tester.run_regression_test()

    # 根据测试结果设置退出码
    if results["errors"] > 0:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
