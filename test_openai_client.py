import os
from openai import OpenAI

# 读取第一个图片的base64数据
with open('test_img1_base64.txt', 'r', encoding='utf-8') as f:
    img1_base64 = f.read().strip()

# 读取第二个图片的base64数据
with open('test_img2_base64.txt', 'r', encoding='utf-8') as f:
    img2_base64 = f.read().strip()

client = OpenAI(base_url="http://************:8080/v1", api_key="")
messages = [
    {
        "role": "user",
        "content": [
            {
                "type": "image_url",
                "image_url": {
                    "url": img1_base64
                }
            },
            {
                "type": "text",
                "text": "检查所见"
            }
        ]
    },
    {
        "role": "assistant",
        "content": [
            {
                "type": "text",
                "text": "眼轴：右眼22.43mm,左眼22.52mm\n曲率：右眼K1（42.14D@174°），K2（45.55D@84°）\n左眼K1（42.12D@6°），K2（44.78D@96°）"
            }
        ]
    },
    {
        "role": "user",
        "content": [
            {
                "type": "image_url",
                "image_url": {
                    "url": img2_base64
                }
            },
            {
                "type": "text",
                "text": "检查所见"
            }
        ]
    }
]
completion = client.chat.completions.create(
    model="MiniCPM-V-4_5-F16.gguf",
    messages=messages,
    top_p=0.8,
    temperature=0.7,
)

print(completion.choices[0].message.content)
